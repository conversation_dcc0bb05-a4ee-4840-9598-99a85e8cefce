import { createClient } from '@supabase/supabase-js'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

// ✅ Logs sécurisés (sans données sensibles)
if (process.env.NODE_ENV === 'development') {
  console.log('🔧 Supabase Config:', {
    hasUrl: !!supabaseUrl,
    hasKey: !!supabaseAnonKey
  })
}

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Variables d\'environnement:', {
    VITE_SUPABASE_URL: supabaseUrl,
    VITE_SUPABASE_ANON_KEY: supabaseAnonKey
  })
  throw new Error('Les variables d\'environnement Supabase sont manquantes')
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
    // Configuration entreprise : sessions plus sécurisées
    storageKey: 'zest-companion-auth',
    storage: window.localStorage
  }
})

export type Database = {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          first_name: string
          last_name: string
          profession: string
          company_name: string
          email: string
          role: Database['public']['Enums']['user_role']
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          first_name: string
          last_name: string
          profession: string
          company_name: string
          email: string
          role?: Database['public']['Enums']['user_role']
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          first_name?: string
          last_name?: string
          profession?: string
          company_name?: string
          email?: string
          role?: Database['public']['Enums']['user_role']
          created_at?: string
          updated_at?: string
        }
      }
    }
    Enums: {
      user_role: 'user' | 'admin' | 'super_admin'
    }
  }
}
