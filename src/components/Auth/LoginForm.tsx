import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '../../contexts/AuthContext'
import { Target, Users, TrendingUp, Eye, EyeOff, Mail, Lock } from 'lucide-react'
import { getLoginErrorMessage, shouldSuggestRegistration } from '../../utils/errorMessages'
import { loginRateLimiter } from '../../utils/rateLimiter'

interface LoginFormProps {
  onSwitchToSignUp: () => void
  onSwitchToForgotPassword: () => void
}

const LoginForm: React.FC<LoginFormProps> = ({ onSwitchToSignUp, onSwitchToForgotPassword }) => {
  const navigate = useNavigate();
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [showSignUpSuggestion, setShowSignUpSuggestion] = useState(false)

  const { signIn, user } = useAuth()

  // Rediriger vers dashboard si déjà connecté
  useEffect(() => {
    if (user) {
      navigate('/dashboard', { replace: true });
    }
  }, [user, navigate]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // ✅ Vérification rate limiting
    if (!loginRateLimiter.checkAttempt(email)) {
      const remainingTime = loginRateLimiter.getRemainingTime(email)
      const minutes = Math.floor(remainingTime / 60)
      const seconds = remainingTime % 60
      const timeText = minutes > 0 ? `${minutes}m ${seconds}s` : `${seconds}s`
      setError(`Too many login attempts. Please wait ${timeText} before trying again.`)
      return
    }

    setLoading(true)
    setError(null)
    setShowSignUpSuggestion(false)

    const { error } = await signIn(email, password)

    if (error) {
      setError(getLoginErrorMessage(error))
      setShowSignUpSuggestion(shouldSuggestRegistration(error))
    } else {
      // ✅ Réinitialiser le rate limiter après connexion réussie
      loginRateLimiter.reset(email)
      // Connexion réussie, redirection vers dashboard
      navigate('/dashboard', { replace: true });
    }

    setLoading(false)
  }

  return (
    <div className="min-h-screen flex">
      {/* Partie gauche - Formulaire */}
      <div className="flex-1 flex flex-col justify-center py-12 px-4 sm:px-6 lg:flex-none lg:px-20 xl:px-24 bg-white min-h-screen">
        <div className="mx-auto w-full max-w-sm lg:w-96 pt-16">
          {/* Logo */}
          <div className="flex items-center gap-2 mb-8">
            <img
              src="/assets/zest-logo.png"
              alt="ZEST COMPANION Logo"
              className="w-8 h-8 object-contain"
            />
            <span className="text-xl text-gray-800 leading-none" style={{ letterSpacing: '1px' }}>
              <span className="font-bold">ZEST</span> <span className="text-gray-600">COMPANION</span>
            </span>
          </div>

          {/* Title */}
          <div className="mb-8">
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              Welcome back!
            </h1>
            <p className="text-gray-600 text-sm">
              Sign in to your account to continue your learning journey.
            </p>
          </div>

          {/* Formulaire */}
          <form onSubmit={handleSubmit} className="space-y-6">
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md text-sm">
                {error}
                {showSignUpSuggestion && (
                  <div className="mt-3 pt-3 border-t border-red-200">
                    <button
                      type="button"
                      onClick={onSwitchToSignUp}
                      className="w-full bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-md transition-colors text-sm"
                    >
                      Create Account
                    </button>
                  </div>
                )}
              </div>
            )}

            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                Email address
              </label>
              <div className="mt-1 relative">
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="appearance-none relative block w-full px-3 py-2 pl-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-purple-500 focus:border-purple-500 focus:z-10 sm:text-sm"
                  placeholder="Enter your email"
                />
                <Mail className="absolute left-3 top-2.5 h-4 w-4 text-gray-400 pointer-events-none z-20" />
              </div>
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                Password
              </label>
              <div className="mt-1 relative">
                <input
                  id="password"
                  name="password"
                  type={showPassword ? "text" : "password"}
                  autoComplete="current-password"
                  required
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="appearance-none relative block w-full px-3 py-2 pl-10 pr-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-purple-500 focus:border-purple-500 focus:z-10 sm:text-sm"
                  placeholder="••••••••"
                />
                <Lock className="absolute left-3 top-2.5 h-4 w-4 text-gray-400 pointer-events-none z-20" />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-3 flex items-center z-20"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4 text-gray-400" />
                  ) : (
                    <Eye className="h-4 w-4 text-gray-400" />
                  )}
                </button>
              </div>
            </div>

            {/* Forgot password link */}
            <div className="flex items-center justify-end">
              <button
                type="button"
                onClick={onSwitchToForgotPassword}
                className="text-sm text-purple-600 hover:text-purple-500"
              >
                Forgot password?
              </button>
            </div>

            <button
              type="submit"
              disabled={loading}
              className="w-full flex justify-center py-2.5 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Signing in...' : 'Sign in'}
            </button>
          </form>

          {/* Registration link */}
          <p className="mt-6 text-center text-sm text-gray-600">
            Don't have an account?{' '}
            <button
              onClick={onSwitchToSignUp}
              className="font-medium text-purple-600 hover:text-purple-500"
            >
              Sign up
            </button>
          </p>

          {/* Terms and Privacy notice */}
          <p className="mt-6 text-center text-xs text-gray-500 leading-relaxed">
            By continuing, you agree to ZEST Companion's{' '}
            <a
              href="/terms-of-service"
              className="text-purple-600 hover:text-purple-500 underline"
              target="_blank"
              rel="noopener noreferrer"
            >
              Terms of Service
            </a>
            {' '}and{' '}
            <a
              href="/privacy-policy"
              className="text-purple-600 hover:text-purple-500 underline"
              target="_blank"
              rel="noopener noreferrer"
            >
              Privacy Policy
            </a>
            , and to receive periodic emails with updates.
          </p>
        </div>
      </div>

      {/* Partie droite - Présentation */}
      <div className="hidden lg:block relative w-0 flex-1 bg-gradient-to-br from-purple-600 to-purple-800">
        <div className="absolute inset-0 flex flex-col justify-center px-12 text-white">
          {/* Logo circulaire */}
          <div className="mb-8 flex justify-center">
            <div className="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
              <img
                src="/assets/zest-logo.png"
                alt="ZEST Logo"
                className="w-8 h-8 object-contain"
              />
            </div>
          </div>

          {/* Titre principal */}
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">
              Welcome to<br />
              ZEST Companion
            </h2>
            <p className="text-purple-100 text-lg">
              Your learning and personal development<br />
              platform to excel in leadership.
            </p>
          </div>

          {/* Cartes de fonctionnalités */}
          <div className="space-y-4 max-w-sm mx-auto">
            <div className="bg-white bg-opacity-10 backdrop-blur-sm rounded-lg p-4 border border-white border-opacity-20">
              <div className="flex items-center space-x-3">
                <div className="flex-shrink-0">
                  <Target className="h-6 w-6 text-orange-300" />
                </div>
                <div>
                  <h3 className="font-semibold text-white">Personalized Learning</h3>
                  <p className="text-purple-100 text-sm">
                    Develop your skills with modules adapted to your needs.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white bg-opacity-10 backdrop-blur-sm rounded-lg p-4 border border-white border-opacity-20">
              <div className="flex items-center space-x-3">
                <div className="flex-shrink-0">
                  <TrendingUp className="h-6 w-6 text-green-300" />
                </div>
                <div>
                  <h3 className="font-semibold text-white">Progress Tracking</h3>
                  <p className="text-purple-100 text-sm">
                    Measure your progress and celebrate your achievements.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white bg-opacity-10 backdrop-blur-sm rounded-lg p-4 border border-white border-opacity-20">
              <div className="flex items-center space-x-3">
                <div className="flex-shrink-0">
                  <Users className="h-6 w-6 text-yellow-300" />
                </div>
                <div>
                  <h3 className="font-semibold text-white">Team Collaboration</h3>
                  <p className="text-purple-100 text-sm">
                    Work together to achieve excellence.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default LoginForm
